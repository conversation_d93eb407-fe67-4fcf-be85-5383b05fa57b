<template>
  <div class="test-voice-page">
    <div class="container">
      <h2>语音功能测试</h2>
      
      <div class="test-section">
        <h3>语音录制测试</h3>
        <MessageEditor @send="handleSend" @send-image="handleSendImage" @send-voice="handleSendVoice" />
      </div>

      <div class="test-section">
        <h3>语音消息显示测试</h3>
        <div class="message-list">
          <MessageItem 
            v-for="message in testMessages" 
            :key="message.id" 
            :message="message" 
            :is-own="message.isOwn"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import MessageEditor from './components/MessageEditor.vue'
import MessageItem from './components/MessageItem.vue'
import { ElMessage } from 'element-plus'

const testMessages = ref([
  {
    id: 1,
    userId: '1',
    nickname: '测试用户',
    avatar: '',
    content: '这是一条文本消息',
    type: 'text',
    createdAt: new Date(),
    isOwn: false,
    status: 'sent'
  },
  {
    id: 2,
    userId: '2',
    nickname: '我',
    avatar: '',
    content: JSON.stringify({
      url: 'https://example.com/voice.mp3',
      duration: 15
    }),
    type: 'voice',
    createdAt: new Date(),
    isOwn: true,
    status: 'sent'
  }
])

const handleSend = (content) => {
  console.log('发送文本消息:', content)
  ElMessage.success('文本消息发送成功')
  
  // 添加到测试消息列表
  testMessages.value.push({
    id: Date.now(),
    userId: '2',
    nickname: '我',
    avatar: '',
    content: content,
    type: 'text',
    createdAt: new Date(),
    isOwn: true,
    status: 'sent'
  })
}

const handleSendImage = (imageUrl) => {
  console.log('发送图片消息:', imageUrl)
  ElMessage.success('图片消息发送成功')
  
  // 添加到测试消息列表
  testMessages.value.push({
    id: Date.now(),
    userId: '2',
    nickname: '我',
    avatar: '',
    content: imageUrl,
    type: 'image',
    createdAt: new Date(),
    isOwn: true,
    status: 'sent'
  })
}

const handleSendVoice = (voiceData) => {
  console.log('发送语音消息:', voiceData)
  ElMessage.success(`语音消息发送成功，时长: ${voiceData.duration}秒`)
  
  // 添加到测试消息列表
  testMessages.value.push({
    id: Date.now(),
    userId: '2',
    nickname: '我',
    avatar: '',
    content: JSON.stringify(voiceData),
    type: 'voice',
    createdAt: new Date(),
    isOwn: true,
    status: 'sent'
  })
}
</script>

<style lang="scss" scoped>
.test-voice-page {
  padding: 20px;
  background: #1f2937;
  min-height: 100vh;
  color: #f3f4f6;
}

.container {
  max-width: 800px;
  margin: 0 auto;
}

h2 {
  text-align: center;
  margin-bottom: 30px;
  color: #22c55e;
}

.test-section {
  margin-bottom: 40px;
  padding: 20px;
  background: #374151;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

  h3 {
    margin-bottom: 20px;
    color: #60a5fa;
    border-bottom: 2px solid #60a5fa;
    padding-bottom: 8px;
  }
}

.message-list {
  max-height: 400px;
  overflow-y: auto;
  padding: 10px;
  background: #4b5563;
  border-radius: 8px;
}
</style>
