<template>
  <div class="message-editor">
    <!-- 工具栏 -->
    <div class="editor-toolbar">
      <div class="toolbar-left">
        <el-button type="text" size="small" @click="handleEmojiClick" class="toolbar-btn">
          <el-icon :size="20">
            <PictureRounded />
          </el-icon>
        </el-button>

        <el-upload :show-file-list="false" :before-upload="handleImageUpload" accept="image/*" class="upload-btn">
          <el-button type="text" size="small" class="toolbar-btn">
            <el-icon :size="20">
              <Picture />
            </el-icon>
          </el-button>
        </el-upload>

        <el-button
          type="text"
          size="small"
          @click="toggleRecording"
          class="toolbar-btn voice-btn"
          :class="{ 'recording': isRecording }"
          :title="isRecording ? '点击结束录音' : '点击开始录音'"
        >
          <el-icon :size="20">
            <Microphone />
          </el-icon>
          <span v-if="isRecording" class="recording-text">{{ recordingDuration }}s</span>
        </el-button>
        <!-- <el-upload
          :show-file-list="false"
          :before-upload="handleVoice"
          class="upload-btn"
        >
          <el-button type="text" size="small" class="toolbar-btn">
            <el-icon :size="20"><Microphone /></el-icon>
          </el-button>
        </el-upload> -->
      </div>

      <div class="toolbar-right">
        <span class="char-count">{{ messageContent.length }}/500</span>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="editor-input">
      <el-input v-model="messageContent" type="textarea" :rows="3" resize="none" placeholder="参与讨论，请输入内容（按Ctrl+Enter发送）"
        @keydown="handleKeydown" @input="handleInput" class="message-textarea" maxlength="500" show-word-limit />
    </div>

    <!-- 发送按钮 -->
    <div class="editor-actions">
      <div class="actions-left">
        <el-text size="small" type="info">
          按 Ctrl + Enter 快速发送
        </el-text>
      </div>
      <div class="actions-right">
        <el-button size="small" @click="handleClear" :disabled="!messageContent.trim()">
          清空
        </el-button>
        <el-button type="primary" size="small" @click="handleSend" :disabled="!messageContent.trim() || sending"
          :loading="sending">
          发送
        </el-button>
      </div>
    </div>

    <!-- 语音录制提示 -->
    <!-- <div v-if="isRecording" class="voice-recording-overlay">
      <div class="recording-modal">
        <div class="recording-animation">
          <div class="wave-circle"></div>
          <div class="wave-circle"></div>
          <div class="wave-circle"></div>
        </div>
        <div class="recording-text">正在录音...</div>
        <div class="recording-duration">{{ formatDuration(recordingDuration) }}</div>
        <div class="recording-tip">松开发送，上滑取消</div>
      </div>
    </div> -->

    <!-- 表情选择器 -->
    <div v-if="showEmojiPicker" class="emoji-picker">
      <div class="emoji-grid">
        <span v-for="emoji in emojiList" :key="emoji" class="emoji-item" @click="insertEmoji(emoji)">
          {{ emoji }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { uploadFile } from '@/api/fileUploadAndDownload.js'

defineOptions({
  name: 'MessageEditor'
})

const emit = defineEmits(['send', 'send-image', 'send-voice'])

// 响应式数据
const messageContent = ref('')
const sending = ref(false)
const showEmojiPicker = ref(false)

// 语音录制相关
const isRecording = ref(false)
const recordingDuration = ref(0)
const mediaRecorder = ref(null)
const audioChunks = ref([])
const recordingTimer = ref(null)
const recordingStartTime = ref(null)

// 常用表情列表
const emojiList = [
  "😀",
  "😃",
  "😄",
  "😁",
  "😆",
  "😅",
  "😂",
  "🤣",
  "😊",
  "😇",
  "🙂",
  "🙃",
  "😉",
  "😌",
  "😍",
  "🥰",
  "😘",
  "😗",
  "😙",
  "😚",
  "😋",
  "😛",
  "😝",
  "😜",
  "🤪",
  "🤨",
  "🧐",
  "🤓",
  "😎",
  "🤩",
  "🥳",
  "😏",
  "😒",
  "😞",
  "😔",
  "😟",
  "😕",
  "🙁",
  "☹️",
  "😣",
  "😖",
  "😫",
  "😩",
  "🥺",
  "😢",
  "😭",
  "😤",
  "😠",
  "😡",
  "🤬",
  "🤯",
  "😳",
  "🥵",
  "🥶",
  "😱",
  "😨",
  "😰",
  "😥",
  "🤗",
]


// 处理键盘事件
const handleKeydown = (event) => {
  // Ctrl + Enter 发送消息
  if (event.ctrlKey && event.key === 'Enter') {
    event.preventDefault()
    handleSend()
  }
  // Esc 关闭表情选择器
  if (event.key === 'Escape') {
    showEmojiPicker.value = false
  }
}

// 处理输入
const handleInput = (value) => {
  // 限制字符数
  if (value.length > 500) {
    messageContent.value = value.slice(0, 500)
  }
}

// 发送消息
const handleSend = async () => {
  if (!messageContent.value.trim()) {
    return
  }

  sending.value = true

  try {
    // 发送消息给父组件处理
    emit('send', messageContent.value.trim())

    // 清空输入框和关闭表情选择器
    messageContent.value = ''
    showEmojiPicker.value = false

    // ElMessage.success('消息发送成功')
  } catch (error) {
    ElMessage.error('消息发送失败')
    console.error('发送消息错误:', error)
  } finally {
    sending.value = false
  }
}

// 清空内容
const handleClear = () => {
  messageContent.value = ''
}

// 表情点击
const handleEmojiClick = () => {
  showEmojiPicker.value = !showEmojiPicker.value
}

// 插入表情
const insertEmoji = (emoji) => {
  messageContent.value += emoji
  showEmojiPicker.value = false
}

// 图片上传
const handleImageUpload = async (file) => {
  try {
    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      ElMessage.error('只支持上传 JPG、PNG、GIF、WebP 格式的图片')
      return false
    }

    // 验证文件大小 (10MB)
    const maxSize = 10 * 1024 * 1024
    if (file.size > maxSize) {
      ElMessage.error('图片大小不能超过 10MB')
      return false
    }

    // 显示上传中提示
    const loadingMessage = ElMessage({
      message: '图片上传中...',
      type: 'info',
      duration: 0
    })

    // 创建 FormData
    const formData = new FormData()
    formData.append('file', file)

    // 调用上传接口
    const response = await uploadFile(formData)

    // 关闭加载提示
    loadingMessage.close()

    if (response && response.data && response.data.file) {
      const imageUrl = response.data.file.url
      ElMessage.success('图片上传成功')

      // 发送图片消息
      emit('send-image', imageUrl)
    } else {
      ElMessage.error('图片上传失败，请重试')
    }

  } catch (error) {
    console.error('图片上传错误:', error)
    ElMessage.error('图片上传失败: ' + (error.message || '未知错误'))
  }

  return false // 阻止默认上传行为
}

// 格式化录音时长
const formatDuration = (seconds) => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 切换录音状态
const toggleRecording = async () => {
  if (isRecording.value) {
    // 如果正在录音，则停止录音
    stopRecording()
  } else {
    // 如果没有录音，则开始录音
    await startRecording()
  }
}

// 开始录音
const startRecording = async () => {
  if (isRecording.value) return

  try {
    // 获取麦克风权限
    const stream = await navigator.mediaDevices.getUserMedia({
      audio: {
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true
      }
    })

    // 创建MediaRecorder实例
    mediaRecorder.value = new MediaRecorder(stream, {
      mimeType: 'audio/webm;codecs=opus'
    })

    // 重置数据
    audioChunks.value = []
    recordingDuration.value = 0
    recordingStartTime.value = Date.now()

    // 开始录制
    mediaRecorder.value.start()
    isRecording.value = true

    // ElMessage.success('开始录音，再次点击结束录制')

    // 开始计时
    recordingTimer.value = setInterval(() => {
      recordingDuration.value = Math.floor((Date.now() - recordingStartTime.value) / 1000)

      // 最长录制60秒
      if (recordingDuration.value >= 60) {
        stopRecording()
      }
    }, 1000)

    // 监听数据
    mediaRecorder.value.ondataavailable = (event) => {
      if (event.data.size > 0) {
        audioChunks.value.push(event.data)
      }
    }

    // 录制完成处理
    mediaRecorder.value.onstop = async () => {
      // 停止所有音频轨道
      stream.getTracks().forEach(track => track.stop())

      // 获取录制时长（移除最短时长限制）
      const duration = Math.floor((Date.now() - recordingStartTime.value) / 1000)

      // 创建音频blob
      const audioBlob = new Blob(audioChunks.value, { type: 'audio/webm;codecs=opus' })

      // 转换为MP3格式并上传
      await convertAndUploadAudio(audioBlob, duration)
    }

  } catch (error) {
    console.error('录音失败:', error)
    ElMessage.error('无法访问麦克风，请检查权限设置')
    isRecording.value = false
  }
}

// 停止录音
const stopRecording = () => {
  if (!isRecording.value || !mediaRecorder.value) return

  // 清除计时器
  if (recordingTimer.value) {
    clearInterval(recordingTimer.value)
    recordingTimer.value = null
  }

  // 停止录制
  if (mediaRecorder.value.state === 'recording') {
    mediaRecorder.value.stop()
  }

  isRecording.value = false
  // ElMessage.success('录音结束，正在发送...')
}

// 转换音频格式并上传
const convertAndUploadAudio = async (audioBlob, duration) => {
  try {
    // 显示上传中提示
    // const loadingMessage = ElMessage({
    //   message: '语音上传中...',
    //   type: 'info',
    //   duration: 0
    // })

    // 创建FormData，直接使用webm格式（现代浏览器都支持）
    const formData = new FormData()
    formData.append('file', audioBlob, `voice_${Date.now()}.webm`)

    // 上传文件
    const response = await uploadFile(formData)

    // 关闭加载提示
    // loadingMessage.close()

    if (response && response.data && response.data.file) {
      const audioUrl = response.data.file.url
      // ElMessage.success('语音发送成功')

      // 发送语音消息，包含时长信息
      emit('send-voice', {
        url: audioUrl,
        duration: duration
      })
    } else {
      ElMessage.error('语音上传失败，请重试')
    }

  } catch (error) {
    console.error('语音上传错误:', error)
    ElMessage.error('语音上传失败: ' + (error.message || '未知错误'))
  }
}


</script>

<style lang="scss" scoped>
.message-editor {
  position: relative;
  background: #4b5563;
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  border-bottom: 1px solid #6b7280;

  .toolbar-left {
    display: flex;
    gap: 4px;

    .toolbar-btn {
      padding: 4px 8px;
      color: #9ca3af;

      &:hover {
        color: #22c55e;
        background: #374151;
      }
    }

    .upload-btn {
      display: inline-block;
    }

    .voice-btn {
      transition: all 0.3s ease;
      position: relative;

      &.recording {
        color: #ef4444;
        background: rgba(239, 68, 68, 0.1);
        animation: pulse 1s infinite;
      }

      .recording-text {
        position: absolute;
        top: -20px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 10px;
        color: #ef4444;
        background: rgba(0, 0, 0, 0.8);
        padding: 2px 6px;
        border-radius: 4px;
        white-space: nowrap;
      }
    }
  }

  .toolbar-right {
    .char-count {
      font-size: 12px;
      color: #9ca3af;
    }
  }
}

.editor-input {
  padding: 0 16px;

  .message-textarea {
    :deep(.el-textarea__inner) {
      border: none;
      box-shadow: none;
      resize: none;
      padding: 12px 0;
      font-size: 14px;
      line-height: 1.5;
      background: #4b5563;
      color: #f3f4f6;

      &:focus {
        box-shadow: none;
      }

      &::placeholder {
        color: #9ca3af;
      }
    }

    :deep(.el-input__count) {
      display: none;
    }
  }
}

.editor-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  border-top: 1px solid #6b7280;

  .actions-left {
    font-size: 12px;
    color: #9ca3af;
  }

  .actions-right {
    display: flex;
    gap: 8px;
  }
}

.emoji-picker {
  position: absolute;
  bottom: 100%;
  left: 16px;
  background: #374151;
  border: 1px solid #6b7280;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  padding: 12px;
  max-width: 300px;

  .emoji-grid {
    display: grid;
    grid-template-columns: repeat(10, 1fr);
    gap: 4px;

    .emoji-item {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      cursor: pointer;
      border-radius: 4px;
      font-size: 16px;
      transition: background-color 0.2s;

      &:hover {
        background: #4b5563;
      }
    }
  }
}

// 语音录制覆盖层
.voice-recording-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  .recording-modal {
    background: #374151;
    border-radius: 16px;
    padding: 32px;
    text-align: center;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    .recording-animation {
      position: relative;
      width: 80px;
      height: 80px;
      margin: 0 auto 16px;

      .wave-circle {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        border: 2px solid #ef4444;
        border-radius: 50%;
        animation: wave 2s infinite ease-out;

        &:nth-child(1) {
          width: 40px;
          height: 40px;
          animation-delay: 0s;
        }

        &:nth-child(2) {
          width: 60px;
          height: 60px;
          animation-delay: 0.5s;
        }

        &:nth-child(3) {
          width: 80px;
          height: 80px;
          animation-delay: 1s;
        }
      }
    }

    .recording-text {
      color: #f3f4f6;
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 8px;
    }

    .recording-duration {
      color: #ef4444;
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 16px;
      font-family: 'Courier New', monospace;
    }

    .recording-tip {
      color: #9ca3af;
      font-size: 14px;
    }
  }
}

// 动画定义
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes wave {
  0% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(0);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(1);
  }
}
</style>
